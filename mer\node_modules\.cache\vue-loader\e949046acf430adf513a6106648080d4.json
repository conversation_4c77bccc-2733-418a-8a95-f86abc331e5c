{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=template&id=f7b5f20a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750506790852}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-row\",\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 24 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"规格类型：\"), props: \"spec_type\" } },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"spec-type-main\" },\n                    [\n                      _c(\n                        \"el-radio\",\n                        {\n                          staticClass: \"service-package-radio\",\n                          attrs: { label: 2 },\n                          model: {\n                            value: _vm.formValidate.spec_type,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.formValidate, \"spec_type\", $$v)\n                            },\n                            expression: \"formValidate.spec_type\",\n                          },\n                        },\n                        [\n                          _c(\"span\", { staticClass: \"service-package-label\" }, [\n                            _vm._v(_vm._s(_vm.$t(\"服务包模式\"))),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"span\", { staticClass: \"service-package-desc\" }, [\n                            _vm._v(\"（推荐）类似Fiverr的服务包选择\"),\n                          ]),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.showDeveloperOptions,\n                          expression: \"showDeveloperOptions\",\n                        },\n                      ],\n                      staticClass: \"spec-type-developer\",\n                    },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"developer-title\" },\n                        [\n                          _c(\"span\", [_vm._v(\"开发者选项\")]),\n                          _vm._v(\" \"),\n                          _c(\n                            \"el-button\",\n                            {\n                              staticClass: \"toggle-btn\",\n                              attrs: { type: \"text\", size: \"mini\" },\n                              on: { click: _vm.toggleDeveloperOptions },\n                            },\n                            [\n                              _vm._v(\n                                \"\\n              \" +\n                                  _vm._s(\n                                    _vm.showDeveloperOptions ? \"收起\" : \"展开\"\n                                  ) +\n                                  \"\\n            \"\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          staticClass: \"developer-radios\",\n                          model: {\n                            value: _vm.formValidate.spec_type,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.formValidate, \"spec_type\", $$v)\n                            },\n                            expression: \"formValidate.spec_type\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-radio\",\n                            { staticClass: \"radio\", attrs: { label: 0 } },\n                            [_vm._v(_vm._s(_vm.$t(\"单规格\")))]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\"el-radio\", { attrs: { label: 1 } }, [\n                            _vm._v(_vm._s(_vm.$t(\"多规格\"))),\n                          ]),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _vm.formValidate.spec_type == 1 && _vm.ruleList.length > 0\n                        ? _c(\n                            \"el-dropdown\",\n                            {\n                              staticClass: \"ml20\",\n                              attrs: { trigger: \"hover\" },\n                              on: { command: _vm.confirm },\n                            },\n                            [\n                              _c(\"span\", { staticClass: \"el-dropdown-link\" }, [\n                                _vm._v(_vm._s(_vm.$t(\"选择规格模板\"))),\n                                _c(\"i\", {\n                                  staticClass:\n                                    \"el-icon-arrow-down el-icon--right\",\n                                }),\n                              ]),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-dropdown-menu\",\n                                {\n                                  attrs: { slot: \"dropdown\" },\n                                  slot: \"dropdown\",\n                                },\n                                [\n                                  _c(\n                                    \"el-scrollbar\",\n                                    {\n                                      staticStyle: {\n                                        \"max-height\": \"300px\",\n                                        \"overflow-y\": \"scroll\",\n                                      },\n                                    },\n                                    _vm._l(\n                                      _vm.ruleList,\n                                      function (item, index) {\n                                        return _c(\n                                          \"el-dropdown-item\",\n                                          {\n                                            key: index,\n                                            attrs: {\n                                              command: item.attr_template_id,\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \"\\n                  \" +\n                                                _vm._s(item.template_name) +\n                                                \"\\n                \"\n                                            ),\n                                          ]\n                                        )\n                                      }\n                                    ),\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: !_vm.showDeveloperOptions,\n                          expression: \"!showDeveloperOptions\",\n                        },\n                      ],\n                      staticClass: \"developer-toggle\",\n                    },\n                    [\n                      _c(\n                        \"el-button\",\n                        {\n                          staticClass: \"toggle-btn\",\n                          attrs: { type: \"text\", size: \"mini\" },\n                          on: { click: _vm.toggleDeveloperOptions },\n                        },\n                        [\n                          _c(\"i\", { staticClass: \"el-icon-setting\" }),\n                          _vm._v(\" 开发者选项\\n          \"),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                ]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.formValidate.spec_type === 1 && _vm.showDeveloperOptions\n            ? _c(\n                \"el-col\",\n                { staticClass: \"noForm\", attrs: { span: 24 } },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: _vm.$t(\"商品规格：\"), required: \"\" } },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"specifications\" },\n                        [\n                          _c(\n                            \"draggable\",\n                            {\n                              attrs: {\n                                group: \"specifications\",\n                                disabled: _vm.attrs.length < 2,\n                                list: _vm.attrs,\n                                handle: \".move-icon\",\n                                animation: \"300\",\n                              },\n                              on: { end: _vm.onMoveSpec },\n                            },\n                            _vm._l(_vm.attrs, function (item, index) {\n                              return _c(\n                                \"div\",\n                                {\n                                  key: index,\n                                  staticClass: \"specifications-item active\",\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.changeCurrentIndex(index)\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\"div\", { staticClass: \"move-icon\" }, [\n                                    _c(\"span\", {\n                                      staticClass: \"iconfont icondrag2\",\n                                    }),\n                                  ]),\n                                  _vm._v(\" \"),\n                                  _c(\"i\", {\n                                    staticClass: \"del el-icon-error\",\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.handleRemoveRole(\n                                          index,\n                                          item.value\n                                        )\n                                      },\n                                    },\n                                  }),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"specifications-item-box\" },\n                                    [\n                                      _c(\"div\", { staticClass: \"lineBox\" }),\n                                      _vm._v(\" \"),\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass:\n                                            \"specifications-item-name mb18\",\n                                        },\n                                        [\n                                          _c(\"el-input\", {\n                                            staticClass:\n                                              \"specifications-item-name-input\",\n                                            attrs: {\n                                              size: \"small\",\n                                              placeholder: _vm.$t(\"规格名称\"),\n                                              maxlength: \"30\",\n                                              \"show-word-limit\": \"\",\n                                            },\n                                            on: {\n                                              change: function ($event) {\n                                                return _vm.attrChangeValue(\n                                                  index,\n                                                  item.value\n                                                )\n                                              },\n                                              focus: function ($event) {\n                                                return _vm.handleFocus(\n                                                  item.value\n                                                )\n                                              },\n                                            },\n                                            model: {\n                                              value: item.value,\n                                              callback: function ($$v) {\n                                                _vm.$set(item, \"value\", $$v)\n                                              },\n                                              expression: \"item.value\",\n                                            },\n                                          }),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"el-checkbox\",\n                                            {\n                                              staticClass: \"ml20\",\n                                              attrs: {\n                                                disabled:\n                                                  !item.add_pic && !_vm.canSel,\n                                                \"true-label\": 1,\n                                                \"false-label\": 0,\n                                              },\n                                              on: {\n                                                change: function (e) {\n                                                  return _vm.addPic(e, index)\n                                                },\n                                              },\n                                              model: {\n                                                value: item.add_pic,\n                                                callback: function ($$v) {\n                                                  _vm.$set(item, \"add_pic\", $$v)\n                                                },\n                                                expression: \"item.add_pic\",\n                                              },\n                                            },\n                                            [\n                                              _vm._v(\n                                                _vm._s(_vm.$t(\"添加规格图\"))\n                                              ),\n                                            ]\n                                          ),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"el-tooltip\",\n                                            {\n                                              staticClass: \"item\",\n                                              attrs: {\n                                                effect: \"dark\",\n                                                content: _vm.$t(\n                                                  \"添加规格图片, 仅支持打开一个(建议尺寸:800*800)\"\n                                                ),\n                                                placement: \"right\",\n                                              },\n                                            },\n                                            [\n                                              _c(\"i\", {\n                                                staticClass: \"el-icon-info\",\n                                              }),\n                                            ]\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                      _vm._v(\" \"),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"rulesBox ml30\" },\n                                        [\n                                          _c(\n                                            \"draggable\",\n                                            {\n                                              staticClass: \"item\",\n                                              attrs: {\n                                                list: item.detail,\n                                                disabled:\n                                                  item.detail.length < 2,\n                                                handle: \".drag\",\n                                              },\n                                              on: { end: _vm.onMoveSpec },\n                                            },\n                                            [\n                                              _vm._l(\n                                                item.detail,\n                                                function (det, indexn) {\n                                                  return _c(\n                                                    \"div\",\n                                                    {\n                                                      key: indexn,\n                                                      staticClass:\n                                                        \"mr10 spec drag\",\n                                                    },\n                                                    [\n                                                      _c(\"i\", {\n                                                        staticClass:\n                                                          \"el-icon-error\",\n                                                        on: {\n                                                          click: function (\n                                                            $event\n                                                          ) {\n                                                            return _vm.handleRemove2(\n                                                              item.detail,\n                                                              indexn,\n                                                              det.value\n                                                            )\n                                                          },\n                                                        },\n                                                      }),\n                                                      _vm._v(\" \"),\n                                                      _c(\n                                                        \"el-input\",\n                                                        {\n                                                          staticStyle: {\n                                                            width: \"120px\",\n                                                          },\n                                                          attrs: {\n                                                            size: \"small\",\n                                                            placeholder:\n                                                              _vm.$t(\"规格值\"),\n                                                            maxlength: \"30\",\n                                                          },\n                                                          on: {\n                                                            change: function (\n                                                              $event\n                                                            ) {\n                                                              return _vm.attrDetailChangeValue(\n                                                                det.value,\n                                                                index\n                                                              )\n                                                            },\n                                                            focus: function (\n                                                              $event\n                                                            ) {\n                                                              return _vm.handleFocus(\n                                                                det.value\n                                                              )\n                                                            },\n                                                            blur: function (\n                                                              $event\n                                                            ) {\n                                                              return _vm.handleBlur()\n                                                            },\n                                                          },\n                                                          model: {\n                                                            value: det.value,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                det,\n                                                                \"value\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"det.value\",\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"template\",\n                                                            { slot: \"prefix\" },\n                                                            [\n                                                              _c(\"span\", {\n                                                                staticClass:\n                                                                  \"iconfont icondrag2\",\n                                                              }),\n                                                            ]\n                                                          ),\n                                                        ],\n                                                        2\n                                                      ),\n                                                      _vm._v(\" \"),\n                                                      item.add_pic\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"img-popover\",\n                                                            },\n                                                            [\n                                                              _c(\"div\", {\n                                                                staticClass:\n                                                                  \"popper-arrow\",\n                                                              }),\n                                                              _vm._v(\" \"),\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"popper\",\n                                                                  on: {\n                                                                    click:\n                                                                      function (\n                                                                        $event\n                                                                      ) {\n                                                                        return _vm.handleSelImg(\n                                                                          det,\n                                                                          index,\n                                                                          indexn\n                                                                        )\n                                                                      },\n                                                                  },\n                                                                },\n                                                                [\n                                                                  det.pic\n                                                                    ? _c(\n                                                                        \"img\",\n                                                                        {\n                                                                          staticClass:\n                                                                            \"img\",\n                                                                          attrs:\n                                                                            {\n                                                                              src: det.pic,\n                                                                            },\n                                                                        }\n                                                                      )\n                                                                    : _c(\"i\", {\n                                                                        staticClass:\n                                                                          \"el-icon-plus\",\n                                                                      }),\n                                                                ]\n                                                              ),\n                                                              _vm._v(\" \"),\n                                                              det.pic\n                                                                ? _c(\"i\", {\n                                                                    staticClass:\n                                                                      \"img-del el-icon-error\",\n                                                                    on: {\n                                                                      click:\n                                                                        function (\n                                                                          $event\n                                                                        ) {\n                                                                          return _vm.handleRemoveImg(\n                                                                            det,\n                                                                            index,\n                                                                            indexn\n                                                                          )\n                                                                        },\n                                                                    },\n                                                                  })\n                                                                : _vm._e(),\n                                                            ]\n                                                          )\n                                                        : _vm._e(),\n                                                    ],\n                                                    1\n                                                  )\n                                                }\n                                              ),\n                                              _vm._v(\" \"),\n                                              _c(\n                                                \"el-popover\",\n                                                {\n                                                  ref: \"popoverRef_\" + index,\n                                                  refInFor: true,\n                                                  attrs: {\n                                                    placement: \"\",\n                                                    width: \"210\",\n                                                    trigger: \"click\",\n                                                  },\n                                                  on: {\n                                                    \"after-enter\": function (\n                                                      $event\n                                                    ) {\n                                                      return _vm.handleShowPop(\n                                                        index\n                                                      )\n                                                    },\n                                                  },\n                                                },\n                                                [\n                                                  _c(\"el-input\", {\n                                                    ref: \"inputRef_\" + index,\n                                                    refInFor: true,\n                                                    staticStyle: {\n                                                      \"min-width\": \"80px\",\n                                                      width: \"210\",\n                                                    },\n                                                    attrs: {\n                                                      size: \"small\",\n                                                      placeholder:\n                                                        _vm.$t(\"请输入规格值\"),\n                                                      maxlength: \"30\",\n                                                      \"show-word-limit\": \"\",\n                                                    },\n                                                    on: {\n                                                      blur: function ($event) {\n                                                        return _vm.createAttr(\n                                                          _vm.formDynamic\n                                                            .attrsVal,\n                                                          index\n                                                        )\n                                                      },\n                                                    },\n                                                    nativeOn: {\n                                                      keyup: function ($event) {\n                                                        if (\n                                                          !$event.type.indexOf(\n                                                            \"key\"\n                                                          ) &&\n                                                          _vm._k(\n                                                            $event.keyCode,\n                                                            \"enter\",\n                                                            13,\n                                                            $event.key,\n                                                            \"Enter\"\n                                                          )\n                                                        ) {\n                                                          return null\n                                                        }\n                                                        return _vm.createAttr(\n                                                          _vm.formDynamic\n                                                            .attrsVal,\n                                                          index\n                                                        )\n                                                      },\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.formDynamic\n                                                          .attrsVal,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.formDynamic,\n                                                          \"attrsVal\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"formDynamic.attrsVal\",\n                                                    },\n                                                  }),\n                                                  _vm._v(\" \"),\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass: \"addfont\",\n                                                      attrs: {\n                                                        slot: \"reference\",\n                                                      },\n                                                      slot: \"reference\",\n                                                    },\n                                                    [\n                                                      _vm._v(\n                                                        _vm._s(\n                                                          _vm.$t(\"添加规格值\")\n                                                        )\n                                                      ),\n                                                    ]\n                                                  ),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            2\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                    ]\n                                  ),\n                                ]\n                              )\n                            }),\n                            0\n                          ),\n                          _vm._v(\" \"),\n                          _vm.attrs.length < 4\n                            ? _c(\n                                \"el-button\",\n                                {\n                                  attrs: { size: \"small\", type: \"text\" },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.handleAddRole()\n                                    },\n                                  },\n                                },\n                                [_vm._v(_vm._s(_vm.$t(\"添加新规格\")))]\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _vm.attrs.length >= 1\n                            ? _c(\n                                \"el-button\",\n                                {\n                                  attrs: { size: \"small\", type: \"text\" },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.handleSaveAsTemplate()\n                                    },\n                                  },\n                                },\n                                [_vm._v(_vm._s(_vm.$t(\"另存为模板\")))]\n                              )\n                            : _vm._e(),\n                        ],\n                        1\n                      ),\n                    ]\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.formValidate.spec_type === 2\n            ? _c(\n                \"el-col\",\n                { staticClass: \"noForm\", attrs: { span: 24 } },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: _vm.$t(\"服务包配置：\"), required: \"\" } },\n                    [\n                      _c(\"div\", { staticClass: \"service-package-config\" }, [\n                        _c(\n                          \"div\",\n                          { staticClass: \"advanced-packages\" },\n                          [\n                            _c(\"div\", { staticClass: \"packages-header\" }, [\n                              _c(\"h3\", [_vm._v(\"服务包配置\")]),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-row\",\n                              {\n                                staticClass: \"packages-row\",\n                                attrs: { gutter: 20 },\n                              },\n                              [\n                                _c(\n                                  \"el-col\",\n                                  { attrs: { span: 8 } },\n                                  [\n                                    _c(\n                                      \"el-card\",\n                                      {\n                                        staticClass: \"package-card basic-card\",\n                                      },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"package-header\",\n                                            attrs: { slot: \"header\" },\n                                            slot: \"header\",\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"package-title\" },\n                                              [_vm._v(\"Basic\")]\n                                            ),\n                                            _vm._v(\" \"),\n                                            _c(\"el-switch\", {\n                                              model: {\n                                                value:\n                                                  _vm.packageConfig.basic\n                                                    .enabled,\n                                                callback: function ($$v) {\n                                                  _vm.$set(\n                                                    _vm.packageConfig.basic,\n                                                    \"enabled\",\n                                                    $$v\n                                                  )\n                                                },\n                                                expression:\n                                                  \"packageConfig.basic.enabled\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _vm.packageConfig.basic.enabled\n                                          ? _c(\n                                              \"div\",\n                                              [\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"包名称\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      attrs: {\n                                                        placeholder:\n                                                          \"Basic套餐名称\",\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .basic.name,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .basic,\n                                                            \"name\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.basic.name\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"价格\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input-number\", {\n                                                      staticStyle: {\n                                                        width: \"100%\",\n                                                      },\n                                                      attrs: {\n                                                        min: 0,\n                                                        \"controls-position\":\n                                                          \"right\",\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .basic.price,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .basic,\n                                                            \"price\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.basic.price\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"交付时间\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-select\",\n                                                      {\n                                                        staticStyle: {\n                                                          width: \"100%\",\n                                                        },\n                                                        model: {\n                                                          value:\n                                                            _vm.packageConfig\n                                                              .basic\n                                                              .delivery_time,\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              _vm.packageConfig\n                                                                .basic,\n                                                              \"delivery_time\",\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"packageConfig.basic.delivery_time\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"1天\",\n                                                            value: \"1天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"2天\",\n                                                            value: \"2天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"3天\",\n                                                            value: \"3天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"4天\",\n                                                            value: \"4天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"5天\",\n                                                            value: \"5天\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"修改次数\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-select\",\n                                                      {\n                                                        staticStyle: {\n                                                          width: \"100%\",\n                                                        },\n                                                        model: {\n                                                          value:\n                                                            _vm.packageConfig\n                                                              .basic.revisions,\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              _vm.packageConfig\n                                                                .basic,\n                                                              \"revisions\",\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"packageConfig.basic.revisions\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"1次\",\n                                                            value: \"1\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"2次\",\n                                                            value: \"2\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"3次\",\n                                                            value: \"3\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"描述\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      attrs: {\n                                                        type: \"textarea\",\n                                                        rows: 2,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .basic.description,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .basic,\n                                                            \"description\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.basic.description\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                              ],\n                                              1\n                                            )\n                                          : _vm._e(),\n                                      ]\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-col\",\n                                  { attrs: { span: 8 } },\n                                  [\n                                    _c(\n                                      \"el-card\",\n                                      {\n                                        staticClass:\n                                          \"package-card standard-card\",\n                                      },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"package-header\",\n                                            attrs: { slot: \"header\" },\n                                            slot: \"header\",\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"package-title\" },\n                                              [_vm._v(\"Standard\")]\n                                            ),\n                                            _vm._v(\" \"),\n                                            _c(\"el-switch\", {\n                                              model: {\n                                                value:\n                                                  _vm.packageConfig.standard\n                                                    .enabled,\n                                                callback: function ($$v) {\n                                                  _vm.$set(\n                                                    _vm.packageConfig.standard,\n                                                    \"enabled\",\n                                                    $$v\n                                                  )\n                                                },\n                                                expression:\n                                                  \"packageConfig.standard.enabled\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _vm.packageConfig.standard.enabled\n                                          ? _c(\n                                              \"div\",\n                                              [\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"包名称\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      attrs: {\n                                                        placeholder:\n                                                          \"Standard套餐名称\",\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .standard.name,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .standard,\n                                                            \"name\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.standard.name\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"价格\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input-number\", {\n                                                      staticStyle: {\n                                                        width: \"100%\",\n                                                      },\n                                                      attrs: {\n                                                        min: 0,\n                                                        \"controls-position\":\n                                                          \"right\",\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .standard.price,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .standard,\n                                                            \"price\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.standard.price\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"交付时间\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-select\",\n                                                      {\n                                                        staticStyle: {\n                                                          width: \"100%\",\n                                                        },\n                                                        model: {\n                                                          value:\n                                                            _vm.packageConfig\n                                                              .standard\n                                                              .delivery_time,\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              _vm.packageConfig\n                                                                .standard,\n                                                              \"delivery_time\",\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"packageConfig.standard.delivery_time\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"3天\",\n                                                            value: \"3天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"5天\",\n                                                            value: \"5天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"7天\",\n                                                            value: \"7天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"10天\",\n                                                            value: \"10天\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"修改次数\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-select\",\n                                                      {\n                                                        staticStyle: {\n                                                          width: \"100%\",\n                                                        },\n                                                        model: {\n                                                          value:\n                                                            _vm.packageConfig\n                                                              .standard\n                                                              .revisions,\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              _vm.packageConfig\n                                                                .standard,\n                                                              \"revisions\",\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"packageConfig.standard.revisions\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"3次\",\n                                                            value: \"3\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"5次\",\n                                                            value: \"5\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"10次\",\n                                                            value: \"10\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"描述\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      attrs: {\n                                                        type: \"textarea\",\n                                                        rows: 2,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .standard\n                                                            .description,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .standard,\n                                                            \"description\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.standard.description\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                              ],\n                                              1\n                                            )\n                                          : _vm._e(),\n                                      ]\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-col\",\n                                  { attrs: { span: 8 } },\n                                  [\n                                    _c(\n                                      \"el-card\",\n                                      {\n                                        staticClass:\n                                          \"package-card premium-card\",\n                                      },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"package-header\",\n                                            attrs: { slot: \"header\" },\n                                            slot: \"header\",\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"package-title\" },\n                                              [_vm._v(\"Premium\")]\n                                            ),\n                                            _vm._v(\" \"),\n                                            _c(\"el-switch\", {\n                                              model: {\n                                                value:\n                                                  _vm.packageConfig.premium\n                                                    .enabled,\n                                                callback: function ($$v) {\n                                                  _vm.$set(\n                                                    _vm.packageConfig.premium,\n                                                    \"enabled\",\n                                                    $$v\n                                                  )\n                                                },\n                                                expression:\n                                                  \"packageConfig.premium.enabled\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                        _vm._v(\" \"),\n                                        _vm.packageConfig.premium.enabled\n                                          ? _c(\n                                              \"div\",\n                                              [\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"包名称\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      attrs: {\n                                                        placeholder:\n                                                          \"Premium套餐名称\",\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .premium.name,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .premium,\n                                                            \"name\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.premium.name\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"价格\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input-number\", {\n                                                      staticStyle: {\n                                                        width: \"100%\",\n                                                      },\n                                                      attrs: {\n                                                        min: 0,\n                                                        \"controls-position\":\n                                                          \"right\",\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .premium.price,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .premium,\n                                                            \"price\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.premium.price\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"交付时间\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-select\",\n                                                      {\n                                                        staticStyle: {\n                                                          width: \"100%\",\n                                                        },\n                                                        model: {\n                                                          value:\n                                                            _vm.packageConfig\n                                                              .premium\n                                                              .delivery_time,\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              _vm.packageConfig\n                                                                .premium,\n                                                              \"delivery_time\",\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"packageConfig.premium.delivery_time\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"5天\",\n                                                            value: \"5天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"7天\",\n                                                            value: \"7天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"10天\",\n                                                            value: \"10天\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"15天\",\n                                                            value: \"15天\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"修改次数\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"el-select\",\n                                                      {\n                                                        staticStyle: {\n                                                          width: \"100%\",\n                                                        },\n                                                        model: {\n                                                          value:\n                                                            _vm.packageConfig\n                                                              .premium\n                                                              .revisions,\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              _vm.packageConfig\n                                                                .premium,\n                                                              \"revisions\",\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"packageConfig.premium.revisions\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"5次\",\n                                                            value: \"5\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"10次\",\n                                                            value: \"10\",\n                                                          },\n                                                        }),\n                                                        _vm._v(\" \"),\n                                                        _c(\"el-option\", {\n                                                          attrs: {\n                                                            label: \"无限次\",\n                                                            value: \"无限\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      label: \"描述\",\n                                                      size: \"small\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      attrs: {\n                                                        type: \"textarea\",\n                                                        rows: 2,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          _vm.packageConfig\n                                                            .premium\n                                                            .description,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            _vm.packageConfig\n                                                              .premium,\n                                                            \"description\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"packageConfig.premium.description\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                              ],\n                                              1\n                                            )\n                                          : _vm._e(),\n                                      ]\n                                    ),\n                                  ],\n                                  1\n                                ),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _vm.showAdvancedPackages\n                          ? _c(\n                              \"div\",\n                              { staticClass: \"extra-services-config\" },\n                              [\n                                _c(\n                                  \"el-divider\",\n                                  { attrs: { \"content-position\": \"left\" } },\n                                  [_vm._v(\"额外服务配置（最多2个大类）\")]\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-row\",\n                                  { attrs: { gutter: 20 } },\n                                  [\n                                    _c(\n                                      \"el-col\",\n                                      { attrs: { span: 12 } },\n                                      [\n                                        _c(\n                                          \"el-card\",\n                                          { staticClass: \"extra-service-card\" },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"card-header\",\n                                                attrs: { slot: \"header\" },\n                                                slot: \"header\",\n                                              },\n                                              [\n                                                _c(\"span\", [\n                                                  _vm._v(\"额外快速交付时间\"),\n                                                ]),\n                                                _vm._v(\" \"),\n                                                _c(\"el-switch\", {\n                                                  model: {\n                                                    value:\n                                                      _vm.extraServices\n                                                        .fastDelivery.enabled,\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        _vm.extraServices\n                                                          .fastDelivery,\n                                                        \"enabled\",\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"extraServices.fastDelivery.enabled\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                            _vm._v(\" \"),\n                                            _vm.extraServices.fastDelivery\n                                              .enabled\n                                              ? _c(\n                                                  \"div\",\n                                                  [\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"服务名称\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-input\", {\n                                                          attrs: {\n                                                            placeholder:\n                                                              \"如：加急处理\",\n                                                          },\n                                                          model: {\n                                                            value:\n                                                              _vm.extraServices\n                                                                .fastDelivery\n                                                                .name,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                _vm\n                                                                  .extraServices\n                                                                  .fastDelivery,\n                                                                \"name\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"extraServices.fastDelivery.name\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"额外价格\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-input-number\", {\n                                                          staticStyle: {\n                                                            width: \"100%\",\n                                                          },\n                                                          attrs: {\n                                                            min: 0,\n                                                            \"controls-position\":\n                                                              \"right\",\n                                                          },\n                                                          model: {\n                                                            value:\n                                                              _vm.extraServices\n                                                                .fastDelivery\n                                                                .price,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                _vm\n                                                                  .extraServices\n                                                                  .fastDelivery,\n                                                                \"price\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"extraServices.fastDelivery.price\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"交付时间选项\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\n                                                          \"el-checkbox-group\",\n                                                          {\n                                                            model: {\n                                                              value:\n                                                                _vm\n                                                                  .extraServices\n                                                                  .fastDelivery\n                                                                  .options,\n                                                              callback:\n                                                                function ($$v) {\n                                                                  _vm.$set(\n                                                                    _vm\n                                                                      .extraServices\n                                                                      .fastDelivery,\n                                                                    \"options\",\n                                                                    $$v\n                                                                  )\n                                                                },\n                                                              expression:\n                                                                \"extraServices.fastDelivery.options\",\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"1天之内\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"1天之内\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"2天之内\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"2天之内\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"3天之内\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"3天之内\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                )\n                                              : _vm._e(),\n                                          ]\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      { attrs: { span: 12 } },\n                                      [\n                                        _c(\n                                          \"el-card\",\n                                          { staticClass: \"extra-service-card\" },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"card-header\",\n                                                attrs: { slot: \"header\" },\n                                                slot: \"header\",\n                                              },\n                                              [\n                                                _c(\"span\", [\n                                                  _vm._v(\"追加修改次数\"),\n                                                ]),\n                                                _vm._v(\" \"),\n                                                _c(\"el-switch\", {\n                                                  model: {\n                                                    value:\n                                                      _vm.extraServices\n                                                        .additionalRevisions\n                                                        .enabled,\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        _vm.extraServices\n                                                          .additionalRevisions,\n                                                        \"enabled\",\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"extraServices.additionalRevisions.enabled\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                            _vm._v(\" \"),\n                                            _vm.extraServices\n                                              .additionalRevisions.enabled\n                                              ? _c(\n                                                  \"div\",\n                                                  [\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"服务名称\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-input\", {\n                                                          attrs: {\n                                                            placeholder:\n                                                              \"如：额外修改\",\n                                                          },\n                                                          model: {\n                                                            value:\n                                                              _vm.extraServices\n                                                                .additionalRevisions\n                                                                .name,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                _vm\n                                                                  .extraServices\n                                                                  .additionalRevisions,\n                                                                \"name\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"extraServices.additionalRevisions.name\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"额外价格\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-input-number\", {\n                                                          staticStyle: {\n                                                            width: \"100%\",\n                                                          },\n                                                          attrs: {\n                                                            min: 0,\n                                                            \"controls-position\":\n                                                              \"right\",\n                                                          },\n                                                          model: {\n                                                            value:\n                                                              _vm.extraServices\n                                                                .additionalRevisions\n                                                                .price,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                _vm\n                                                                  .extraServices\n                                                                  .additionalRevisions,\n                                                                \"price\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"extraServices.additionalRevisions.price\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"修改次数选项\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\n                                                          \"el-checkbox-group\",\n                                                          {\n                                                            model: {\n                                                              value:\n                                                                _vm\n                                                                  .extraServices\n                                                                  .additionalRevisions\n                                                                  .options,\n                                                              callback:\n                                                                function ($$v) {\n                                                                  _vm.$set(\n                                                                    _vm\n                                                                      .extraServices\n                                                                      .additionalRevisions,\n                                                                    \"options\",\n                                                                    $$v\n                                                                  )\n                                                                },\n                                                              expression:\n                                                                \"extraServices.additionalRevisions.options\",\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"1次修改\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"1次修改\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"2次修改\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"2次修改\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"3次修改\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"3次修改\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"5次修改\",\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"5次修改\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                )\n                                              : _vm._e(),\n                                          ]\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-row\",\n                                  {\n                                    staticStyle: { \"margin-top\": \"20px\" },\n                                    attrs: { gutter: 20 },\n                                  },\n                                  [\n                                    _c(\n                                      \"el-col\",\n                                      { attrs: { span: 12 } },\n                                      [\n                                        _c(\n                                          \"el-card\",\n                                          { staticClass: \"extra-service-card\" },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"card-header\",\n                                                attrs: { slot: \"header\" },\n                                                slot: \"header\",\n                                              },\n                                              [\n                                                _c(\"span\", [\n                                                  _vm._v(\"服务保障期\"),\n                                                ]),\n                                                _vm._v(\" \"),\n                                                _c(\"el-switch\", {\n                                                  model: {\n                                                    value:\n                                                      _vm.extraServices.warranty\n                                                        .enabled,\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        _vm.extraServices\n                                                          .warranty,\n                                                        \"enabled\",\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"extraServices.warranty.enabled\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                            _vm._v(\" \"),\n                                            _vm.extraServices.warranty.enabled\n                                              ? _c(\n                                                  \"div\",\n                                                  [\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"服务名称\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-input\", {\n                                                          attrs: {\n                                                            placeholder:\n                                                              \"如：延长保障\",\n                                                          },\n                                                          model: {\n                                                            value:\n                                                              _vm.extraServices\n                                                                .warranty.name,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                _vm\n                                                                  .extraServices\n                                                                  .warranty,\n                                                                \"name\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"extraServices.warranty.name\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"额外价格\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"el-input-number\", {\n                                                          staticStyle: {\n                                                            width: \"100%\",\n                                                          },\n                                                          attrs: {\n                                                            min: 0,\n                                                            \"controls-position\":\n                                                              \"right\",\n                                                          },\n                                                          model: {\n                                                            value:\n                                                              _vm.extraServices\n                                                                .warranty.price,\n                                                            callback: function (\n                                                              $$v\n                                                            ) {\n                                                              _vm.$set(\n                                                                _vm\n                                                                  .extraServices\n                                                                  .warranty,\n                                                                \"price\",\n                                                                $$v\n                                                              )\n                                                            },\n                                                            expression:\n                                                              \"extraServices.warranty.price\",\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    _c(\n                                                      \"el-form-item\",\n                                                      {\n                                                        attrs: {\n                                                          label: \"保障期选项\",\n                                                          size: \"small\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\n                                                          \"el-checkbox-group\",\n                                                          {\n                                                            model: {\n                                                              value:\n                                                                _vm\n                                                                  .extraServices\n                                                                  .warranty\n                                                                  .options,\n                                                              callback:\n                                                                function ($$v) {\n                                                                  _vm.$set(\n                                                                    _vm\n                                                                      .extraServices\n                                                                      .warranty,\n                                                                    \"options\",\n                                                                    $$v\n                                                                  )\n                                                                },\n                                                              expression:\n                                                                \"extraServices.warranty.options\",\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label:\n                                                                    \"3个月\",\n                                                                },\n                                                              },\n                                                              [_vm._v(\"3个月\")]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label: \"半年\",\n                                                                },\n                                                              },\n                                                              [_vm._v(\"半年\")]\n                                                            ),\n                                                            _vm._v(\" \"),\n                                                            _c(\n                                                              \"el-checkbox\",\n                                                              {\n                                                                attrs: {\n                                                                  label: \"一年\",\n                                                                },\n                                                              },\n                                                              [_vm._v(\"一年\")]\n                                                            ),\n                                                          ],\n                                                          1\n                                                        ),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ],\n                                                  1\n                                                )\n                                              : _vm._e(),\n                                          ]\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"generate-specs-btn\",\n                                    staticStyle: {\n                                      \"margin-top\": \"20px\",\n                                      \"text-align\": \"center\",\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"el-button\",\n                                      {\n                                        on: {\n                                          click: _vm.previewServicePackage,\n                                        },\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n                预览配置\\n              \"\n                                        ),\n                                      ]\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"p\",\n                                      {\n                                        staticStyle: {\n                                          \"margin-top\": \"10px\",\n                                          color: \"#909399\",\n                                          \"font-size\": \"12px\",\n                                        },\n                                      },\n                                      [\n                                        _c(\"i\", {\n                                          staticClass: \"el-icon-info\",\n                                        }),\n                                        _vm._v(\n                                          ' 点击\"下一步\"将自动生成服务包规格\\n              '\n                                        ),\n                                      ]\n                                    ),\n                                  ],\n                                  1\n                                ),\n                              ],\n                              1\n                            )\n                          : _vm._e(),\n                      ]),\n                    ]\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"el-col\",\n            { attrs: { xl: 24, lg: 24, md: 24, sm: 24, xs: 24 } },\n            [\n              _vm.formValidate.spec_type === 0\n                ? _c(\n                    \"el-form-item\",\n                    [\n                      _c(\n                        \"el-table\",\n                        {\n                          staticClass: \"tabNumWidth\",\n                          attrs: {\n                            data: _vm.OneattrValue,\n                            border: \"\",\n                            size: \"mini\",\n                          },\n                        },\n                        [\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              align: \"center\",\n                              label: _vm.$t(\"图片\"),\n                              \"min-width\": \"80\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass:\n                                            \"upLoadPicBox specPictrue\",\n                                          on: {\n                                            click: function ($event) {\n                                              return _vm.modalPicTap(\"1\", \"dan\")\n                                            },\n                                          },\n                                        },\n                                        [\n                                          _vm.formValidate.image\n                                            ? _c(\n                                                \"div\",\n                                                {\n                                                  staticClass: \"pictrue tabPic\",\n                                                },\n                                                [\n                                                  _c(\"img\", {\n                                                    attrs: {\n                                                      src: scope.row.image,\n                                                    },\n                                                  }),\n                                                ]\n                                              )\n                                            : _c(\n                                                \"div\",\n                                                {\n                                                  staticClass: \"upLoad tabPic\",\n                                                },\n                                                [\n                                                  _c(\"i\", {\n                                                    staticClass:\n                                                      \"el-icon-camera cameraIconfont\",\n                                                  }),\n                                                ]\n                                              ),\n                                        ]\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              1897791643\n                            ),\n                          }),\n                          _vm._v(\" \"),\n                          _vm._l(_vm.attrValue, function (item, iii) {\n                            return _c(\"el-table-column\", {\n                              key: iii,\n                              attrs: {\n                                label:\n                                  _vm.formThead[iii] &&\n                                  _vm.formThead[iii].title,\n                                align: \"center\",\n                                \"min-width\": \"110\",\n                              },\n                              scopedSlots: _vm._u(\n                                [\n                                  {\n                                    key: \"default\",\n                                    fn: function (scope) {\n                                      return [\n                                        _vm.formValidate.svip_price_type != 0 &&\n                                        _vm.formThead[iii]\n                                          ? _c(\n                                              \"div\",\n                                              [\n                                                _vm.formThead[iii].title ===\n                                                _vm.$t(\"规格编码\")\n                                                  ? _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: { type: \"text\" },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm._e(),\n                                                _vm._v(\" \"),\n                                                _vm.formThead[iii].title ===\n                                                _vm.$t(\"条形码\")\n                                                  ? _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: { type: \"text\" },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm._e(),\n                                                _vm._v(\" \"),\n                                                _vm.formThead[iii].title !==\n                                                  \"付费会员价\" &&\n                                                _vm.formThead[iii].title !==\n                                                  \"规格编码\" &&\n                                                _vm.formThead[iii].title !==\n                                                  \"条形码\" &&\n                                                _vm.formThead[iii].title !==\n                                                  _vm.$t(\"库存\")\n                                                  ? _c(\"el-input-number\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        min: 0,\n                                                        size: \"small\",\n                                                        \"controls-position\":\n                                                          \"right\",\n                                                      },\n                                                      on: {\n                                                        blur: function (\n                                                          $event\n                                                        ) {\n                                                          return _vm.memberPrice(\n                                                            _vm.formThead[iii],\n                                                            scope.row\n                                                          )\n                                                        },\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm._e(),\n                                                _vm._v(\" \"),\n                                                _vm.formThead[iii].title ===\n                                                  _vm.$t(\"库存\") &&\n                                                (_vm.formValidate.type == 2 ||\n                                                  _vm.formValidate.type == 3)\n                                                  ? _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        disabled: \"\",\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm.formThead[iii].title ===\n                                                      _vm.$t(\"库存\") &&\n                                                    _vm.formValidate.type !==\n                                                      2 &&\n                                                    _vm.formValidate.type !== 3\n                                                  ? _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm._e(),\n                                              ],\n                                              1\n                                            )\n                                          : _c(\n                                              \"div\",\n                                              [\n                                                _vm.formThead[iii].title ===\n                                                _vm.$t(\"规格编码\")\n                                                  ? _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm.formThead[iii].title ===\n                                                    _vm.$t(\"条形码\")\n                                                  ? _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    })\n                                                  : _c(\"el-input-number\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        min: 0,\n                                                        size: \"small\",\n                                                        disabled:\n                                                          _vm.formThead[iii]\n                                                            .title === \"库存\" &&\n                                                          (_vm.formValidate\n                                                            .type == 2 ||\n                                                            _vm.formValidate\n                                                              .type == 3),\n                                                        \"controls-position\":\n                                                          \"right\",\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    }),\n                                              ],\n                                              1\n                                            ),\n                                      ]\n                                    },\n                                  },\n                                ],\n                                null,\n                                true\n                              ),\n                            })\n                          }),\n                          _vm._v(\" \"),\n                          _vm.formValidate.type == 2\n                            ? [\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    align: \"center\",\n                                    label: _vm.$t(\"云盘设置\"),\n                                    \"min-width\": \"120\",\n                                  },\n                                  scopedSlots: _vm._u(\n                                    [\n                                      {\n                                        key: \"default\",\n                                        fn: function (scope) {\n                                          return [\n                                            scope.row.cdkey &&\n                                            !scope.row.cdkey.list &&\n                                            !scope.row.stock\n                                              ? _c(\n                                                  \"el-button\",\n                                                  {\n                                                    attrs: { size: \"small\" },\n                                                    on: {\n                                                      click: function ($event) {\n                                                        return _vm.addVirtual(\n                                                          0,\n                                                          0,\n                                                          \"OneattrValue\"\n                                                        )\n                                                      },\n                                                    },\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(_vm.$t(\"添加链接\"))\n                                                    ),\n                                                  ]\n                                                )\n                                              : _c(\n                                                  \"span\",\n                                                  {\n                                                    staticClass: \"seeCatMy\",\n                                                    on: {\n                                                      click: function ($event) {\n                                                        return _vm.seeVirtual(\n                                                          0,\n                                                          _vm.OneattrValue[0],\n                                                          \"OneattrValue\",\n                                                          0\n                                                        )\n                                                      },\n                                                    },\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(_vm.$t(\"已设置\"))\n                                                    ),\n                                                  ]\n                                                ),\n                                          ]\n                                        },\n                                      },\n                                    ],\n                                    null,\n                                    false,\n                                    325098137\n                                  ),\n                                }),\n                              ]\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _vm.formValidate.type == 3\n                            ? [\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    align: \"center\",\n                                    label: _vm.$t(\"卡密设置\"),\n                                    \"min-width\": \"140\",\n                                  },\n                                  scopedSlots: _vm._u(\n                                    [\n                                      {\n                                        key: \"default\",\n                                        fn: function (scope) {\n                                          return [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                attrs: {\n                                                  placeholder:\n                                                    _vm.$t(\"请选择卡密库\"),\n                                                  clearable: \"\",\n                                                  size: \"small\",\n                                                },\n                                                on: {\n                                                  change: function ($event) {\n                                                    return _vm.handleChange(\n                                                      $event,\n                                                      scope.$index,\n                                                      \"OneattrValue\"\n                                                    )\n                                                  },\n                                                },\n                                                model: {\n                                                  value: scope.row.library_id,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      scope.row,\n                                                      \"library_id\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"scope.row.library_id\",\n                                                },\n                                              },\n                                              _vm._l(\n                                                _vm.cdkeyLibraryList,\n                                                function (item, index) {\n                                                  return _c(\"el-option\", {\n                                                    key: index,\n                                                    attrs: {\n                                                      value: item.id,\n                                                      label: item.name,\n                                                      disabled:\n                                                        !item.checkout &&\n                                                        (item.product_id !=\n                                                          _vm.product_id ||\n                                                          (item.product_id ==\n                                                            _vm.product_id &&\n                                                            _vm.$route.query\n                                                              .type == \"copy\")),\n                                                    },\n                                                  })\n                                                }\n                                              ),\n                                              1\n                                            ),\n                                          ]\n                                        },\n                                      },\n                                    ],\n                                    null,\n                                    false,\n                                    261220766\n                                  ),\n                                }),\n                              ]\n                            : _vm._e(),\n                        ],\n                        2\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.formValidate.spec_type == 1 && _vm.showDeveloperOptions\n                ? _c(\n                    \"el-form-item\",\n                    {\n                      staticClass: \"labeltop\",\n                      attrs: { label: _vm.$t(\"规格列表：\") },\n                    },\n                    [\n                      _c(\n                        \"el-table\",\n                        {\n                          key: _vm.tableKey,\n                          staticStyle: { width: \"100%\" },\n                          attrs: {\n                            data: _vm.ManyAttrValue,\n                            \"cell-class-name\": _vm.tableCellClassName,\n                            \"span-method\": _vm.objectSpanMethod,\n                            border: \"\",\n                            size: \"small\",\n                          },\n                        },\n                        _vm._l(_vm.formValidate.header, function (item, index) {\n                          return _c(\"el-table-column\", {\n                            key: index,\n                            attrs: {\n                              label: item.title,\n                              \"min-width\": item.minWidth || \"100\",\n                              fixed: item.fixed,\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      scope.$index == 0\n                                        ? [\n                                            item.key\n                                              ? [\n                                                  _vm.attrs.length &&\n                                                  _vm.attrs[\n                                                    scope.column.index\n                                                  ] &&\n                                                  _vm.ManyAttrValue.length\n                                                    ? _c(\n                                                        \"div\",\n                                                        [\n                                                          _c(\n                                                            \"el-select\",\n                                                            {\n                                                              attrs: {\n                                                                placeholder:\n                                                                  \"请选择\" +\n                                                                  item.title,\n                                                                size: \"small\",\n                                                                clearable: \"\",\n                                                              },\n                                                              model: {\n                                                                value:\n                                                                  _vm\n                                                                    .oneFormBatch[0][\n                                                                    item.title\n                                                                  ],\n                                                                callback:\n                                                                  function (\n                                                                    $$v\n                                                                  ) {\n                                                                    _vm.$set(\n                                                                      _vm\n                                                                        .oneFormBatch[0],\n                                                                      item.title,\n                                                                      $$v\n                                                                    )\n                                                                  },\n                                                                expression:\n                                                                  \"oneFormBatch[0][item.title]\",\n                                                              },\n                                                            },\n                                                            _vm._l(\n                                                              _vm.attrs[\n                                                                scope.column\n                                                                  .index\n                                                              ].detail,\n                                                              function (val) {\n                                                                return _c(\n                                                                  \"el-option\",\n                                                                  {\n                                                                    key: val.value,\n                                                                    attrs: {\n                                                                      label:\n                                                                        val.value,\n                                                                      value:\n                                                                        val.value,\n                                                                    },\n                                                                  }\n                                                                )\n                                                              }\n                                                            ),\n                                                            1\n                                                          ),\n                                                        ],\n                                                        1\n                                                      )\n                                                    : _vm._e(),\n                                                ]\n                                              : item.slot === \"image\"\n                                              ? [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass:\n                                                        \"upLoadPicBox specPictrue\",\n                                                      on: {\n                                                        click: function (\n                                                          $event\n                                                        ) {\n                                                          $event.stopPropagation()\n                                                          return _vm.modalPicTap(\n                                                            \"1\",\n                                                            \"pi\",\n                                                            scope.$index\n                                                          )\n                                                        },\n                                                      },\n                                                    },\n                                                    [\n                                                      _vm.oneFormBatch[0].image\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"upLoad tabPic\",\n                                                            },\n                                                            [\n                                                              _c(\"img\", {\n                                                                directives: [\n                                                                  {\n                                                                    name: \"lazy\",\n                                                                    rawName:\n                                                                      \"v-lazy\",\n                                                                    value:\n                                                                      _vm\n                                                                        .oneFormBatch[0]\n                                                                        .image,\n                                                                    expression:\n                                                                      \"oneFormBatch[0].image\",\n                                                                  },\n                                                                ],\n                                                              }),\n                                                              _vm._v(\" \"),\n                                                              _c(\"i\", {\n                                                                staticClass:\n                                                                  \"el-icon-error btndel btnclose\",\n                                                                on: {\n                                                                  click:\n                                                                    function (\n                                                                      $event\n                                                                    ) {\n                                                                      $event.stopPropagation()\n                                                                      _vm.oneFormBatch[0].image =\n                                                                        \"\"\n                                                                    },\n                                                                },\n                                                              }),\n                                                            ]\n                                                          )\n                                                        : _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"upLoad tabPic\",\n                                                            },\n                                                            [\n                                                              _c(\"i\", {\n                                                                staticClass:\n                                                                  \"el-icon-camera cameraIconfont\",\n                                                              }),\n                                                            ]\n                                                          ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              : item.slot === \"price\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .price,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"price\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].price\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"cost\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                      clearable: \"\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .cost,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"cost\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].cost\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"ot_price\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                      clearable: \"\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .ot_price,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"ot_price\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].ot_price\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"stock\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      disabled:\n                                                        _vm.formValidate.type ==\n                                                          3 ||\n                                                        _vm.formValidate.type ==\n                                                          2,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                      clearable: \"\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .stock,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"stock\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].stock\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"fictitious\"\n                                              ? [\n                                                  _vm._v(\n                                                    \"\\n                  --\\n                \"\n                                                  ),\n                                                ]\n                                              : item.slot === \"bar_code\"\n                                              ? [\n                                                  _c(\"el-input\", {\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .bar_code,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"bar_code\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].bar_code\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"bar_code_number\"\n                                              ? [\n                                                  _c(\"el-input\", {\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .bar_code_number,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"bar_code_number\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].bar_code_number\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"weight\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                      clearable: \"\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .weight,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"weight\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].weight\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"volume\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                      clearable: \"\",\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.oneFormBatch[0]\n                                                          .volume,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.oneFormBatch[0],\n                                                          \"volume\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"oneFormBatch[0].volume\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"selected_spec\"\n                                              ? [\n                                                  _vm._v(\n                                                    \"\\n                  --\\n                \"\n                                                  ),\n                                                ]\n                                              : item.slot === \"action\"\n                                              ? [\n                                                  _c(\n                                                    \"el-button\",\n                                                    {\n                                                      attrs: {\n                                                        type: \"text\",\n                                                        size: \"mini\",\n                                                      },\n                                                      on: {\n                                                        click: _vm.batchAdd,\n                                                      },\n                                                    },\n                                                    [\n                                                      _vm._v(\n                                                        _vm._s(\n                                                          _vm.$t(\"批量修改\")\n                                                        )\n                                                      ),\n                                                    ]\n                                                  ),\n                                                  _vm._v(\" \"),\n                                                  _c(\n                                                    \"el-button\",\n                                                    {\n                                                      attrs: {\n                                                        type: \"text\",\n                                                        size: \"mini\",\n                                                      },\n                                                      on: {\n                                                        click: _vm.batchDel,\n                                                      },\n                                                    },\n                                                    [\n                                                      _vm._v(\n                                                        _vm._s(_vm.$t(\"清空\"))\n                                                      ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              : _vm._e(),\n                                          ]\n                                        : [\n                                            item.key\n                                              ? [\n                                                  _c(\"div\", [\n                                                    _c(\"span\", [\n                                                      _vm._v(\n                                                        _vm._s(\n                                                          scope.row.detail &&\n                                                            scope.row.detail[\n                                                              item.key\n                                                            ]\n                                                            ? scope.row.detail[\n                                                                item.key\n                                                              ]\n                                                            : \"\"\n                                                        )\n                                                      ),\n                                                    ]),\n                                                  ]),\n                                                ]\n                                              : _vm._e(),\n                                            _vm._v(\" \"),\n                                            item.slot === \"image\"\n                                              ? [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass:\n                                                        \"upLoadPicBox specPictrue\",\n                                                      on: {\n                                                        click: function (\n                                                          $event\n                                                        ) {\n                                                          return _vm.modalPicTap(\n                                                            \"1\",\n                                                            \"duo\",\n                                                            scope.$index\n                                                          )\n                                                        },\n                                                      },\n                                                    },\n                                                    [\n                                                      scope.row.image ||\n                                                      scope.row.pic\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"upLoad tabPic\",\n                                                            },\n                                                            [\n                                                              _c(\"img\", {\n                                                                attrs: {\n                                                                  src:\n                                                                    scope.row\n                                                                      .image ||\n                                                                    scope.row\n                                                                      .pic,\n                                                                },\n                                                              }),\n                                                            ]\n                                                          )\n                                                        : _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"upLoad tabPic\",\n                                                            },\n                                                            [\n                                                              _c(\"i\", {\n                                                                staticClass:\n                                                                  \"el-icon-camera cameraIconfont\",\n                                                                staticStyle: {\n                                                                  \"font-size\":\n                                                                    \"24px\",\n                                                                },\n                                                              }),\n                                                            ]\n                                                          ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              : _vm._e(),\n                                            _vm._v(\" \"),\n                                            item.slot === \"price\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].price,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"price\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].price\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"cost\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].cost,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"cost\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].cost\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"ot_price\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].ot_price,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"ot_price\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].ot_price\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"stock\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      disabled:\n                                                        _vm.formValidate.type ==\n                                                          3 ||\n                                                        _vm.formValidate.type ==\n                                                          2,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                      precision: 0,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].stock,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"stock\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].stock\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"bar_code\"\n                                              ? [\n                                                  _c(\"el-input\", {\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].bar_code,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"bar_code\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].bar_code\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"bar_code_number\"\n                                              ? [\n                                                  _c(\"el-input\", {\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].bar_code_number,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"bar_code_number\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].bar_code_number\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"weight\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].weight,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"weight\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].weight\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"volume\"\n                                              ? [\n                                                  _c(\"el-input-number\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      controls: false,\n                                                      min: 0,\n                                                      max: 9999999999,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].volume,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"volume\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].volume\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"fictitious\" &&\n                                                _vm.formValidate.type == 2\n                                              ? [\n                                                  !scope.row.cdkey ||\n                                                  (scope.row.cdkey &&\n                                                    !scope.row.cdkey.list &&\n                                                    !scope.row.stock)\n                                                    ? _c(\n                                                        \"el-button\",\n                                                        {\n                                                          attrs: {\n                                                            size: \"small\",\n                                                          },\n                                                          on: {\n                                                            click: function (\n                                                              $event\n                                                            ) {\n                                                              return _vm.addVirtual(\n                                                                0,\n                                                                scope.$index,\n                                                                \"ManyAttrValue\"\n                                                              )\n                                                            },\n                                                          },\n                                                        },\n                                                        [\n                                                          _vm._v(\n                                                            _vm._s(\n                                                              _vm.$t(\"添加链接\")\n                                                            )\n                                                          ),\n                                                        ]\n                                                      )\n                                                    : _c(\n                                                        \"span\",\n                                                        {\n                                                          staticClass:\n                                                            \"seeCatMy\",\n                                                          on: {\n                                                            click: function (\n                                                              $event\n                                                            ) {\n                                                              return _vm.seeVirtual(\n                                                                0,\n                                                                _vm\n                                                                  .ManyAttrValue[\n                                                                  scope.$index\n                                                                ],\n                                                                \"ManyAttrValue\",\n                                                                scope.$index\n                                                              )\n                                                            },\n                                                          },\n                                                        },\n                                                        [\n                                                          _vm._v(\n                                                            _vm._s(\n                                                              _vm.$t(\"已设置\")\n                                                            )\n                                                          ),\n                                                        ]\n                                                      ),\n                                                ]\n                                              : item.slot === \"fictitious\" &&\n                                                _vm.formValidate.type == 3\n                                              ? [\n                                                  _c(\n                                                    \"el-select\",\n                                                    {\n                                                      attrs: {\n                                                        clearable: \"\",\n                                                        placeholder:\n                                                          _vm.$t(\n                                                            \"请选择卡密库\"\n                                                          ),\n                                                        size: \"small\",\n                                                      },\n                                                      on: {\n                                                        change: function (\n                                                          $event\n                                                        ) {\n                                                          return _vm.handleChange(\n                                                            $event,\n                                                            scope.$index,\n                                                            \"ManyAttrValue\"\n                                                          )\n                                                        },\n                                                        \"visible-change\":\n                                                          function ($event) {\n                                                            return _vm.getSelectedLiarbry(\n                                                              _vm.ManyAttrValue[\n                                                                scope.$index\n                                                              ],\n                                                              _vm.ManyAttrValue\n                                                            )\n                                                          },\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          scope.row.library_id,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            \"library_id\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row.library_id\",\n                                                      },\n                                                    },\n                                                    _vm._l(\n                                                      _vm.cdkeyLibraryList,\n                                                      function (item, index) {\n                                                        return _c(\"el-option\", {\n                                                          key: index,\n                                                          attrs: {\n                                                            value: item.id,\n                                                            label: item.name,\n                                                            disabled:\n                                                              (!item.checkout &&\n                                                                (item.product_id !=\n                                                                  _vm.product_id ||\n                                                                  (item.product_id ==\n                                                                    _vm.product_id &&\n                                                                    _vm.$route\n                                                                      .query\n                                                                      .type ==\n                                                                      \"copy\"))) ||\n                                                              (_vm\n                                                                .selectedLibrary\n                                                                .length > 0 &&\n                                                                _vm.selectedLibrary.indexOf(\n                                                                  item.id\n                                                                ) != -1),\n                                                          },\n                                                        })\n                                                      }\n                                                    ),\n                                                    1\n                                                  ),\n                                                ]\n                                              : item.slot === \"selected_spec\"\n                                              ? [\n                                                  _c(\"el-switch\", {\n                                                    attrs: {\n                                                      \"active-value\": 1,\n                                                      \"inactive-value\": 0,\n                                                    },\n                                                    on: {\n                                                      change: function (e) {\n                                                        return _vm.changeDefaultSelect(\n                                                          e,\n                                                          scope.$index\n                                                        )\n                                                      },\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].is_default_select,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"is_default_select\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].is_default_select\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : item.slot === \"action\"\n                                              ? [\n                                                  _c(\"el-switch\", {\n                                                    staticClass: \"defineSwitch\",\n                                                    attrs: {\n                                                      \"active-text\": \"显示\",\n                                                      \"inactive-text\": \"隐藏\",\n                                                      \"active-value\": 1,\n                                                      \"inactive-value\": 0,\n                                                    },\n                                                    on: {\n                                                      change: function (\n                                                        $event\n                                                      ) {\n                                                        return _vm.changeDefaultShow(\n                                                          scope.$index\n                                                        )\n                                                      },\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.ManyAttrValue[\n                                                          scope.$index\n                                                        ].is_show,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.ManyAttrValue[\n                                                            scope.$index\n                                                          ],\n                                                          \"is_show\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"ManyAttrValue[scope.$index].is_show\",\n                                                    },\n                                                  }),\n                                                ]\n                                              : _vm._e(),\n                                          ],\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              true\n                            ),\n                          })\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}